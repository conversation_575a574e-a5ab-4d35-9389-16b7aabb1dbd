{"add_total_row": 1, "add_translate_data": 0, "columns": [{"fieldname": "date", "fieldtype": "Date", "label": "Date", "width": 0}, {"fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Suppliers", "width": 0}, {"fieldname": "challan_no", "fieldtype": "Data", "label": "Challan No", "width": 0}, {"fieldname": "vehicle_no", "fieldtype": "Data", "label": "Vehicle No", "width": 0}, {"fieldname": "time_in", "fieldtype": "Time", "label": "Time In", "width": 0}, {"fieldname": "time_out", "fieldtype": "Time", "label": "Time Out", "width": 0}, {"fieldname": "qty", "fieldtype": "Float", "label": "Qty", "width": 0}, {"fieldname": "remarks", "fieldtype": "Data", "label": "Remarks", "width": 0}], "creation": "2025-09-25 14:41:57.800880", "disabled": 0, "docstatus": 0, "doctype": "Report", "filters": [{"default": "M15", "fieldname": "grade", "fieldtype": "Select", "label": "Grade", "mandatory": 1, "options": "M10\nM15\nM20\nM25\nM30\nM35\nM40\nM45\nM50\nM55\nM60\nM65\nM70\nM75\nM80\nM85\nM90\nM95\nM100", "wildcard_filter": 0}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "mandatory": 1, "options": "Projects", "wildcard_filter": 0}], "idx": 0, "is_standard": "Yes", "json": "{}", "letter_head": "", "letterhead": null, "modified": "2025-09-25 15:10:50.246903", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "RMC", "owner": "Administrator", "prepared_report": 0, "query": "SELECT\n    t2.date,\n    t2.supplier,\n    t2.challan_no,\n    t2.vehicle_no,\n    t2.time_in,\n    t2.time_out,\n    t2.qty,\n    t2.remarks\nFROM\n    `tabRMC` AS t1\nJOIN\n    `tabRmc table` AS t2\nON\n    t1.name = t2.parent\nWHERE\n    t1.grade = %(grade)s\n    AND (%(project)s IS NULL OR %(project)s = '' OR t1.project = %(project)s)\nORDER BY\n    t2.date DESC, t2.time_in DESC;", "ref_doctype": "RMC", "report_name": "RMC", "report_type": "Query Report", "roles": [{"role": "System Manager"}], "timeout": 0}